<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
        <?php if(auth()->guard()->check()): ?>
            <meta name="user-name" content="<?php echo e(auth()->user()->name); ?>">
            <meta name="user-avatar" content="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>">
        <?php endif; ?>

        <title><?php echo e(config('app.name', 'UniLink')); ?></title>

        <!-- Preload critical resources -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link rel="dns-prefetch" href="https://fonts.bunny.net">

        <!-- Fonts with display=swap to prevent layout shift -->
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Critical CSS and JS -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

        <!-- Defer non-critical JavaScript to prevent layout blocking -->
        <script defer src="<?php echo e(asset('js/post-summary-updater.js')); ?>"></script>
        <script defer src="<?php echo e(asset('js/comments.js')); ?>"></script>
        <script defer src="<?php echo e(asset('js/comment-modal.js')); ?>"></script>
        <script defer src="<?php echo e(asset('js/reactions.js')); ?>"></script>
    </head>
    <body class="font-sans antialiased bg-custom-lightest overflow-hidden">
        <!-- Header Navigation -->
        <?php echo $__env->make('layouts.unilink-header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!-- Main 3-Column Container -->
        <div class="flex h-screen pt-16">
            <!-- Left Sidebar -->
            <div class="hidden lg:flex lg:flex-col lg:w-70 xl:w-80 bg-white border-r border-custom-second-darkest overflow-y-auto flex-shrink-0">
                <?php echo $__env->make('layouts.unilink-sidebar-content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>

            <!-- Central Feed -->
            <main class="flex-1 bg-custom-lightest overflow-y-auto min-w-0 scrollbar-hide">
                <div class="max-w-3xl mx-auto px-4 py-6">
                    <!-- Feed Header (if provided) -->
                    <?php if(isset($header)): ?>
                        <div class="mb-6">
                            <?php echo e($header); ?>

                        </div>
                    <?php endif; ?>

                    <!-- Main Feed Content -->
                    <div class="space-y-4">
                        <?php echo e($slot); ?>

                    </div>
                </div>
            </main>

            <!-- Right Sidebar -->
            <div class="hidden lg:flex lg:flex-col lg:w-70 xl:w-80 bg-white border-l border-custom-second-darkest overflow-y-auto flex-shrink-0 scrollbar-hide">
                <?php echo $__env->make('layouts.unilink-right-sidebar-content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>

        <!-- Mobile Sidebar -->
        <div class="lg:hidden">
            <?php echo $__env->make('layouts.unilink-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div x-data="{ open: false }"
             x-on:toggle-sidebar.window="open = !open"
             x-show="open"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-custom-darkest bg-opacity-75 z-30 lg:hidden"
             @click="$dispatch('toggle-sidebar')">
        </div>

        <!-- Notification Popup -->
        <?php echo $__env->make('components.notification-popup', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!-- Image Modal -->
        <?php echo $__env->make('components.image-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </body>
</html>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/feed-layout.blade.php ENDPATH**/ ?>