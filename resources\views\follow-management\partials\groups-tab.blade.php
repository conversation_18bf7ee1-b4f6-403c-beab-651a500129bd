@if($items->count() > 0)
    <div class="divide-y divide-gray-200">
        @foreach($items as $group)
            <div class="p-6 flex items-center justify-between hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-4">
                    <img src="{{ $group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($group->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($group->name) . '&color=10B981&background=D1FAE5&size=128' }}"
                         alt="{{ $group->name }}"
                         class="w-16 h-16 rounded-lg object-cover">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <a href="{{ route('groups.show', $group) }}" 
                               class="hover:text-blue-600 transition-colors">
                                {{ $group->name }}
                            </a>
                        </h3>
                        @if($group->description)
                            <p class="text-gray-600 mt-1">{{ Str::limit($group->description, 120) }}</p>
                        @endif
                        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            <span>{{ $group->activeMembers()->count() }} members</span>
                            <span>{{ $group->approvedPosts()->count() }} posts</span>
                            <span class="capitalize">{{ $group->visibility }} group</span>
                            @if($group->organization)
                                <span>{{ $group->organization->name }}</span>
                            @endif
                            <span>Joined {{ $group->pivot->joined_at ? $group->pivot->joined_at->diffForHumans() : 'recently' }}</span>
                        </div>
                        <div class="flex items-center space-x-2 mt-2">
                            @if($group->visibility === 'private')
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                    </svg>
                                    Private
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                                    </svg>
                                    Public
                                </span>
                            @endif
                            @if($group->pivot->role !== 'member')
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize">
                                    {{ $group->pivot->role }}
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center space-x-3">
                    @if($group->userCanModerate(auth()->user()))
                        <a href="{{ route('groups.members', $group) }}"
                           class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Manage Members
                        </a>
                    @endif
                    <a href="{{ route('groups.show', $group) }}" 
                       class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                        View Group
                    </a>
                    @if($group->created_by !== auth()->id() && !$group->userCanModerate(auth()->user()))
                        <form action="{{ route('groups.leave', $group) }}" method="POST" class="inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    onclick="return confirm('Are you sure you want to leave this group?')"
                                    class="bg-red-100 text-red-700 px-4 py-2 rounded-lg hover:bg-red-200 transition-colors">
                                Leave Group
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        @endforeach
    </div>

    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200">
        {{ $items->links() }}
    </div>
@else
    <div class="p-12 text-center">
        <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">
            @if(request('search'))
                No groups found
            @else
                Not a member of any groups yet
            @endif
        </h3>
        <p class="text-gray-600">
            @if(request('search'))
                Try adjusting your search terms.
            @else
                Join groups to see them here.
            @endif
        </p>
        @if(request('search'))
            <div class="mt-4">
                <a href="{{ route('follow-management.following', ['tab' => 'groups']) }}" 
                   class="text-blue-600 hover:text-blue-800 font-medium">
                    View all groups
                </a>
            </div>
        @else
            <div class="mt-4">
                <a href="{{ route('groups.index') }}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Discover Groups
                </a>
            </div>
        @endif
    </div>
@endif
