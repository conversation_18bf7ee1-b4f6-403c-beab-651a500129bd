@if($items->count() > 0)
    <div class="divide-y divide-gray-200">
        @foreach($items as $user)
            <div class="p-6 flex items-center justify-between hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-4">
                    <img src="{{ $user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&color=7BC74D&background=EEEEEE&size=128' }}"
                         alt="{{ $user->name }}" 
                         class="w-16 h-16 rounded-full object-cover">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <a href="{{ route('profile.user', $user) }}"
                               class="hover:text-blue-600 transition-colors">
                                {{ $user->name }}
                            </a>
                        </h3>
                        @if($user->hasCompleteProfile())
                            <p class="text-gray-600">{{ $user->email }}</p>
                            @if($user->bio)
                                <p class="text-sm text-gray-500 mt-1">{{ Str::limit($user->bio, 40) }}</p>
                            @endif
                            <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                <span>{{ $user->followers()->count() }} followers</span>
                                <span>{{ $user->following()->count() }} following</span>
                                <span>{{ $user->posts()->count() }} posts</span>
                                <span>Following since {{ $user->pivot->created_at->diffForHumans() }}</span>
                            </div>
                        @else
                            <p class="text-sm text-gray-500">Basic profile</p>
                        @endif
                    </div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <livewire:user-follower :user="$user" :key="'following-user-' . $user->id" />
                    <a href="{{ route('profile.user', $user) }}" 
                       class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                        View Profile
                    </a>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200">
        {{ $items->links() }}
    </div>
@else
    <div class="p-12 text-center">
        <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">
            @if(request('search'))
                No users found
            @else
                Not following anyone yet
            @endif
        </h3>
        <p class="text-gray-600">
            @if(request('search'))
                Try adjusting your search terms.
            @else
                Start following users to see them here.
            @endif
        </p>
        @if(request('search'))
            <div class="mt-4">
                <a href="{{ route('follow-management.following', ['tab' => 'users']) }}" 
                   class="text-blue-600 hover:text-blue-800 font-medium">
                    View all following
                </a>
            </div>
        @else
            <div class="mt-4">
                <a href="{{ route('dashboard') }}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Discover Users
                </a>
            </div>
        @endif
    </div>
@endif
