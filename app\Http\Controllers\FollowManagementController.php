<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Organization;
use App\Models\Group;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FollowManagementController extends Controller
{
    /**
     * Show the followers management page
     */
    public function followers(Request $request)
    {
        $user = Auth::user();
        $search = $request->get('search');

        // Get followers with search functionality
        $followersQuery = $user->followers()
            ->orderBy('user_followers.created_at', 'desc');

        if ($search) {
            $followersQuery->where(function($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $followers = $followersQuery->paginate(20)->appends($request->query());

        return view('follow-management.followers', compact('followers', 'search'));
    }

    /**
     * Search followers via AJAX
     */
    public function searchFollowers(Request $request)
    {
        $user = Auth::user();
        $query = $request->get('q', '');

        if (empty($query) || strlen($query) < 1) {
            return response()->json([
                'success' => true,
                'followers' => []
            ]);
        }

        // Search followers by name or email
        $followers = $user->followers()
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%");
            })
            ->orderBy('user_followers.created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function($follower) {
                return [
                    'id' => $follower->id,
                    'name' => $follower->name,
                    'email' => $follower->email,
                    'bio' => $follower->bio,
                    'avatar_url' => $follower->avatar ? asset('storage/' . $follower->avatar) : asset('images/default-avatar.png'),
                    'profile_url' => route('profile.user', $follower),
                    'has_complete_profile' => $follower->hasCompleteProfile()
                ];
            });

        return response()->json([
            'success' => true,
            'followers' => $followers
        ]);
    }

    /**
     * Search following via AJAX
     */
    public function searchFollowing(Request $request)
    {
        $user = Auth::user();
        $tab = $request->get('tab', 'users');
        $query = $request->get('q', '');

        if (empty($query) || strlen($query) < 1) {
            return response()->json([
                'success' => true,
                'results' => []
            ]);
        }

        $results = [];

        switch ($tab) {
            case 'users':
                $results = $this->searchFollowingUsers($user, $query);
                break;
            case 'organizations':
                $results = $this->searchFollowingOrganizations($user, $query);
                break;
            case 'groups':
                $results = $this->searchFollowingGroups($user, $query);
                break;
            case 'discover':
                $results = $this->searchDiscoverUsers($user, $query);
                break;
        }

        return response()->json([
            'success' => true,
            'results' => $results
        ]);
    }

    private function searchFollowingUsers($user, $query)
    {
        return $user->following()
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%")
                  ->orWhere('bio', 'like', "%{$query}%");
            })
            ->orderBy('user_followers.created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function($followedUser) {
                return [
                    'id' => $followedUser->id,
                    'name' => $followedUser->name,
                    'email' => $followedUser->email,
                    'bio' => $followedUser->bio,
                    'avatar_url' => $followedUser->avatar ? asset('storage/' . $followedUser->avatar) : asset('images/default-avatar.png'),
                    'profile_url' => route('profile.user', $followedUser),
                    'type' => 'user',
                    'has_complete_profile' => $followedUser->hasCompleteProfile()
                ];
            });
    }

    private function searchFollowingOrganizations($user, $query)
    {
        return $user->activeOrganizations()
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%");
            })
            ->orderBy('organization_members.joined_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function($organization) {
                return [
                    'id' => $organization->id,
                    'name' => $organization->name,
                    'description' => $organization->description,
                    'logo_url' => $organization->logo ? asset('storage/' . $organization->logo) : asset('images/default-organization.png'),
                    'profile_url' => route('organizations.show', $organization),
                    'members_count' => $organization->activeMembers()->count(),
                    'type' => 'organization'
                ];
            });
    }

    private function searchFollowingGroups($user, $query)
    {
        return $user->activeGroups()
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%");
            })
            ->orderBy('group_members.joined_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function($group) {
                return [
                    'id' => $group->id,
                    'name' => $group->name,
                    'description' => $group->description,
                    'logo_url' => $group->logo ? asset('storage/' . $group->logo) : asset('images/default-group.png'),
                    'profile_url' => route('groups.show', $group),
                    'members_count' => $group->activeMembers()->count(),
                    'type' => 'group'
                ];
            });
    }

    private function searchDiscoverUsers($user, $query)
    {
        $followingIds = $user->following()->pluck('users.id')->toArray();

        return User::where('id', '!=', $user->id)
            ->whereNotIn('id', $followingIds)
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%")
                  ->orWhere('bio', 'like', "%{$query}%");
            })
            ->orderBy('updated_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function($discoverUser) {
                return [
                    'id' => $discoverUser->id,
                    'name' => $discoverUser->name,
                    'email' => $discoverUser->email,
                    'bio' => $discoverUser->bio,
                    'avatar_url' => $discoverUser->avatar ? asset('storage/' . $discoverUser->avatar) : asset('images/default-avatar.png'),
                    'profile_url' => route('profile.user', $discoverUser),
                    'followers_count' => $discoverUser->followers()->count(),
                    'type' => 'discover_user',
                    'has_complete_profile' => $discoverUser->hasCompleteProfile()
                ];
            });
    }

    /**
     * Show the following management page with tabs
     */
    public function following(Request $request)
    {
        $user = Auth::user();
        $tab = $request->get('tab', 'users');
        $search = $request->get('search');

        $data = [
            'currentTab' => $tab,
            'search' => $search,
        ];

        switch ($tab) {
            case 'users':
                $data['following'] = $this->getFollowingUsers($user, $search, $request);
                break;
            case 'organizations':
                $data['organizations'] = $this->getFollowedOrganizations($user, $search, $request);
                break;
            case 'groups':
                $data['groups'] = $this->getFollowedGroups($user, $search, $request);
                break;
            case 'discover':
                $data['allUsers'] = $this->getDiscoverUsers($user, $search, $request);
                break;
        }

        return view('follow-management.following', $data);
    }

    /**
     * Get users that the current user is following
     */
    private function getFollowingUsers(User $user, $search, Request $request)
    {
        $query = $user->following()
            ->orderBy('user_followers.created_at', 'desc');

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        return $query->paginate(20)->appends($request->query());
    }

    /**
     * Get organizations that the current user is a member of
     */
    private function getFollowedOrganizations(User $user, $search, Request $request)
    {
        $query = $user->activeOrganizations()
            ->orderBy('organization_members.joined_at', 'desc');

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        return $query->paginate(20)->appends($request->query());
    }

    /**
     * Get groups that the current user is a member of (since groups use membership, not following)
     */
    private function getFollowedGroups(User $user, $search, Request $request)
    {
        $query = $user->activeGroups()
            ->orderBy('group_members.joined_at', 'desc');

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        return $query->paginate(20)->appends($request->query());
    }

    /**
     * Get all users for discovery with follow status
     */
    private function getDiscoverUsers(User $currentUser, $search, Request $request)
    {
        $sort = $request->get('sort', 'recent'); // recent, popular, new, active

        $query = User::where('id', '!=', $currentUser->id)
            ->with(['followers', 'following', 'posts']);

        // Apply search filter
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('bio', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        switch ($sort) {
            case 'popular':
                $query->withCount('followers')
                      ->orderBy('followers_count', 'desc')
                      ->orderBy('created_at', 'desc');
                break;
            case 'new':
                $query->orderBy('created_at', 'desc');
                break;
            case 'active':
                $query->withCount('posts')
                      ->orderBy('posts_count', 'desc')
                      ->orderBy('updated_at', 'desc');
                break;
            case 'recent':
            default:
                $query->orderBy('updated_at', 'desc');
                break;
        }

        $users = $query->paginate(20)->appends($request->query());

        // Add follow status for each user
        $followingIds = $currentUser->following()->pluck('users.id')->toArray();

        foreach ($users as $user) {
            $user->is_followed_by_current_user = in_array($user->id, $followingIds);
        }

        return $users;
    }
}
