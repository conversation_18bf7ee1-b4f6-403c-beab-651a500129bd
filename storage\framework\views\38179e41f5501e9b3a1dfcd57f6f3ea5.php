<?php if($items->count() > 0): ?>
    <div class="divide-y divide-gray-200">
        <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $organization): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="p-6 flex items-center justify-between hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-4">
                    <img src="<?php echo e($organization->logo ? asset('storage/' . $organization->logo) : asset('images/default-organization.png')); ?>" 
                         alt="<?php echo e($organization->name); ?>" 
                         class="w-16 h-16 rounded-lg object-cover">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <a href="<?php echo e(route('organizations.show', $organization)); ?>" 
                               class="hover:text-blue-600 transition-colors">
                                <?php echo e($organization->name); ?>

                            </a>
                        </h3>
                        <?php if($organization->description): ?>
                            <p class="text-gray-600 mt-1"><?php echo e(Str::limit($organization->description, 120)); ?></p>
                        <?php endif; ?>
                        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            <span><?php echo e($organization->followers()->count()); ?> followers</span>
                            <span><?php echo e($organization->activeMembers()->count()); ?> members</span>
                            <span><?php echo e($organization->posts()->count()); ?> posts</span>
                            <span>Following since <?php echo e($organization->pivot->created_at->diffForHumans()); ?></span>
                        </div>
                        <?php if($organization->website): ?>
                            <div class="mt-2">
                                <a href="<?php echo e($organization->website); ?>" 
                                   target="_blank"
                                   class="text-blue-600 hover:text-blue-800 text-sm">
                                    <?php echo e($organization->website); ?>

                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('organization-follower', ['organization' => $organization]);

$__html = app('livewire')->mount($__name, $__params, 'following-org-' . $organization->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    <a href="<?php echo e(route('organizations.show', $organization)); ?>" 
                       class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                        View Organization
                    </a>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200">
        <?php echo e($items->links()); ?>

    </div>
<?php else: ?>
    <div class="p-12 text-center">
        <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">
            <?php if(request('search')): ?>
                No organizations found
            <?php else: ?>
                Not a member of any organizations yet
            <?php endif; ?>
        </h3>
        <p class="text-gray-600">
            <?php if(request('search')): ?>
                Try adjusting your search terms.
            <?php else: ?>
                Join organizations to see them here.
            <?php endif; ?>
        </p>
        <?php if(request('search')): ?>
            <div class="mt-4">
                <a href="<?php echo e(route('follow-management.following', ['tab' => 'organizations'])); ?>" 
                   class="text-blue-600 hover:text-blue-800 font-medium">
                    View all following
                </a>
            </div>
        <?php else: ?>
            <div class="mt-4">
                <a href="<?php echo e(route('organizations.index')); ?>" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Discover Organizations
                </a>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/follow-management/partials/organizations-tab.blade.php ENDPATH**/ ?>