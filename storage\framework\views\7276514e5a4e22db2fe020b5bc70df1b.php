<div class="flex items-center space-x-4">
    <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->check()): ?>
        <button
            wire:click="toggleFollow"
            wire:loading.attr="disabled"
            class="flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 <?php echo e($isFollowing ? 'bg-gray-600 text-white hover:bg-gray-700' : 'bg-blue-600 text-white hover:bg-blue-700'); ?>">

            <!--[if BLOCK]><![endif]--><?php if($isFollowing): ?>
                <!--[if BLOCK]><![endif]--><?php if(in_array($userRole, ['president', 'vice_president', 'secretary', 'treasurer', 'officer'])): ?>
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" wire:loading.remove wire:target="toggleFollow">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
                    </svg>
                    <span wire:loading.remove wire:target="toggleFollow">Manage</span>
                <?php else: ?>
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" wire:loading.remove wire:target="toggleFollow">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    <span wire:loading.remove wire:target="toggleFollow">Joined</span>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            <?php else: ?>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" wire:loading.remove wire:target="toggleFollow">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span wire:loading.remove wire:target="toggleFollow">Join</span>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <span wire:loading wire:target="toggleFollow">
                <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <!--[if BLOCK]><![endif]--><?php if($isFollowing): ?>
                    <!--[if BLOCK]><![endif]--><?php if(in_array($userRole, ['president', 'vice_president', 'secretary', 'treasurer', 'officer'])): ?>
                        Opening...
                    <?php else: ?>
                        Leaving...
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                <?php else: ?>
                    Joining...
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </span>
        </button>
    <?php else: ?>
        <a href="<?php echo e(route('login')); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 flex items-center space-x-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span>Join</span>
        </a>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Followers Count -->
    <div class="flex items-center text-sm text-gray-500">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
        <span><?php echo e(number_format($followersCount)); ?> members</span>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/livewire/organization-follower.blade.php ENDPATH**/ ?>