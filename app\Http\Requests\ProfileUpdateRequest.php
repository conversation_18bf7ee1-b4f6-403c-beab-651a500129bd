<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProfileUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email',
                'max:255',
                Rule::unique(User::class)->ignore($this->user()->id),
            ],
            'bio' => ['nullable', 'string', 'max:500'],
            'phone' => ['nullable', 'string', 'max:20'],
            'student_id' => ['nullable', 'string', 'max:20'],
            'avatar' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'background_photo' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:5120'],

            // Academic Information
            'campus' => ['nullable', 'string', 'max:255'],
            'course_program' => ['nullable', 'string', 'max:255'],
            'year_level' => ['nullable', 'string', 'max:50'],
            'enrollment_status' => ['nullable', 'in:active,loa,graduated'],
            'college_department' => ['nullable', 'string', 'max:255'],

            // Personal Information
            'contact_number' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string', 'max:500'],
            'birthdate' => ['nullable', 'date', 'before:today'],

            // Platform-Specific Info
            'skills_interests' => ['nullable', 'array'],
            'skills_interests.*' => ['string', 'max:100'],

            // Optional Additions
            'social_links' => ['nullable', 'array'],
            'social_links.*.platform' => ['string', 'max:50'],
            'social_links.*.url' => ['url', 'max:255'],
            'achievements' => ['nullable', 'array'],
            'achievements.*' => ['string', 'max:255'],

            // Privacy Settings
            'profile_visibility' => ['nullable', 'array'],
            'profile_visibility.*' => ['boolean'],
        ];
    }
}
