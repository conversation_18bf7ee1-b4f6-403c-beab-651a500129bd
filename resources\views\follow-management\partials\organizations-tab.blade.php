@if($items->count() > 0)
    <div class="divide-y divide-gray-200">
        @foreach($items as $organization)
            <div class="p-6 flex items-center justify-between hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-4">
                    <img src="{{ $organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($organization->name) . '&color=3B82F6&background=DBEAFE&size=128' }}"
                         alt="{{ $organization->name }}"
                         class="w-16 h-16 rounded-lg object-cover">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <a href="{{ route('organizations.show', $organization) }}" 
                               class="hover:text-blue-600 transition-colors">
                                {{ $organization->name }}
                            </a>
                        </h3>
                        @if($organization->description)
                            <p class="text-gray-600 mt-1">{{ Str::limit($organization->description, 4) }}</p>
                        @endif
                        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            <span>{{ $organization->activeMembers()->count() }} members</span>
                            <span>{{ $organization->posts()->count() }} posts</span>
                            <span>Member since {{ $organization->pivot->joined_at->diffForHumans() }}</span>
                        </div>
                        @if($organization->website)
                            <div class="mt-2">
                                <a href="{{ $organization->website }}" 
                                   target="_blank"
                                   class="text-blue-600 hover:text-blue-800 text-sm">
                                    {{ $organization->website }}
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <livewire:organization-follower :organization="$organization" :key="'following-org-' . $organization->id" />
                    <a href="{{ route('organizations.show', $organization) }}"
                       class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                        View Organization
                    </a>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200">
        {{ $items->links() }}
    </div>
@else
    <div class="p-12 text-center">
        <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">
            @if(request('search'))
                No organizations found
            @else
                Not a member of any organizations yet
            @endif
        </h3>
        <p class="text-gray-600">
            @if(request('search'))
                Try adjusting your search terms.
            @else
                Join organizations to see them here.
            @endif
        </p>
        @if(request('search'))
            <div class="mt-4">
                <a href="{{ route('follow-management.following', ['tab' => 'organizations']) }}"
                   class="text-blue-600 hover:text-blue-800 font-medium">
                    View all organizations
                </a>
            </div>
        @else
            <div class="mt-4">
                <a href="{{ route('organizations.index') }}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Discover Organizations
                </a>
            </div>
        @endif
    </div>
@endif
