<!-- Sorting Options -->
<div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
    <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">Discover Users</h3>
        <div class="flex items-center space-x-2">
            <label class="text-sm text-gray-600">Sort by:</label>
            <form method="GET" action="<?php echo e(route('follow-management.following')); ?>" class="inline">
                <input type="hidden" name="tab" value="discover">
                <?php if(request('search')): ?>
                    <input type="hidden" name="search" value="<?php echo e(request('search')); ?>">
                <?php endif; ?>
                <select name="sort" onchange="this.form.submit()" 
                        class="text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    <option value="recent" <?php echo e(request('sort', 'recent') === 'recent' ? 'selected' : ''); ?>>Recently Active</option>
                    <option value="popular" <?php echo e(request('sort') === 'popular' ? 'selected' : ''); ?>>Most Popular</option>
                    <option value="new" <?php echo e(request('sort') === 'new' ? 'selected' : ''); ?>>Newest Users</option>
                    <option value="active" <?php echo e(request('sort') === 'active' ? 'selected' : ''); ?>>Most Active</option>
                </select>
            </form>
        </div>
    </div>
</div>

<?php if($items->count() > 0): ?>
    <div class="divide-y divide-gray-200">
        <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="p-6 flex items-center justify-between hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-4">
                    <img src="<?php echo e($user->avatar ? asset('storage/' . $user->avatar) : asset('images/default-avatar.png')); ?>" 
                         alt="<?php echo e($user->name); ?>" 
                         class="w-16 h-16 rounded-full object-cover">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <h3 class="text-lg font-semibold text-gray-900">
                                <a href="<?php echo e(route('profile.user', $user)); ?>" 
                                   class="hover:text-blue-600 transition-colors">
                                    <?php echo e($user->name); ?>

                                </a>
                            </h3>
                            <?php if($user->is_followed_by_current_user): ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                    Following
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    Not Following
                                </span>
                            <?php endif; ?>
                        </div>
                        <p class="text-gray-600"><?php echo e($user->email); ?></p>
                        <!-- <?php if($user->bio): ?>
                            <p class="text-sm text-gray-500 mt-1"><?php echo e(Str::limit($user->bio, 100)); ?></p>
                        <?php endif; ?> -->
                        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            <span><?php echo e($user->followers()->count()); ?> followers</span>
                            <span><?php echo e($user->following()->count()); ?> following</span>
                            <span><?php echo e($user->posts()->count()); ?> posts</span>
                            <span>Joined <?php echo e($user->created_at->diffForHumans()); ?></span>
                            <?php if($user->updated_at->diffInDays() < 7): ?>
                                <span class="text-green-600 font-medium">Recently Active</span>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Mutual Connections -->
                        <?php
                            $mutualFollowers = auth()->user()->followers()
                                ->whereIn('users.id', $user->followers()->pluck('users.id'))
                                ->limit(3)
                                ->get();
                        ?>
                        <?php if($mutualFollowers->count() > 0): ?>
                            <div class="flex items-center mt-2 text-xs text-gray-500">
                                <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Connections','class' => 'w-3 h-3 mr-1']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Connections','class' => 'w-3 h-3 mr-1']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                                <span>Followed by <?php echo e($mutualFollowers->pluck('name')->join(', ', ' and ')); ?></span>
                                <?php if($mutualFollowers->count() < auth()->user()->followers()->whereIn('users.id', $user->followers()->pluck('users.id'))->count()): ?>
                                    <span> and <?php echo e(auth()->user()->followers()->whereIn('users.id', $user->followers()->pluck('users.id'))->count() - $mutualFollowers->count()); ?> others</span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('user-follower', ['user' => $user]);

$__html = app('livewire')->mount($__name, $__params, 'discover-user-' . $user->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    <a href="<?php echo e(route('profile.user', $user)); ?>" 
                       class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                        View Profile
                    </a>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200">
        <?php echo e($items->links()); ?>

    </div>
<?php else: ?>
    <div class="p-12 text-center">
        <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Discover_Users','class' => 'w-16 h-16 text-gray-300 mx-auto mb-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Discover_Users','class' => 'w-16 h-16 text-gray-300 mx-auto mb-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
        <h3 class="text-lg font-medium text-gray-900 mb-2">
            <?php if(request('search')): ?>
                No users found
            <?php else: ?>
                No users to discover
            <?php endif; ?>
        </h3>
        <p class="text-gray-600">
            <?php if(request('search')): ?>
                Try adjusting your search terms or filters.
            <?php else: ?>
                All users on the platform are already being followed by you.
            <?php endif; ?>
        </p>
        <?php if(request('search')): ?>
            <div class="mt-4">
                <a href="<?php echo e(route('follow-management.following', ['tab' => 'discover'])); ?>" 
                   class="text-blue-600 hover:text-blue-800 font-medium">
                    View all users
                </a>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/follow-management/partials/discover-tab.blade.php ENDPATH**/ ?>