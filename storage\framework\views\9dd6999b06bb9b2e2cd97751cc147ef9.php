<?php if (isset($component)) { $__componentOriginal4969f54a92451522b65593c595a4fb0c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4969f54a92451522b65593c595a4fb0c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.unilink-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Following</h1>
                    <p class="text-gray-600 mt-1">Manage your connections</p>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="<?php echo e(route('follow-management.followers')); ?>" 
                       class="text-blue-600 hover:text-blue-800 font-medium transition-colors">
                        View Followers
                    </a>
                    <a href="<?php echo e(route('profile.show')); ?>" 
                       class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                        Back to Profile
                    </a>
                </div>
            </div>
        </div>

        <!-- Tabs Navigation -->
        <div class="bg-white rounded-lg shadow-sm mb-6">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6" aria-label="Tabs">
                    <a href="<?php echo e(route('follow-management.following', ['tab' => 'users'])); ?>" 
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors <?php echo e($currentTab === 'users' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            <span>Users</span>
                            <?php if(isset($following)): ?>
                                <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs"><?php echo e($following->total()); ?></span>
                            <?php endif; ?>
                        </div>
                    </a>
                    <a href="<?php echo e(route('follow-management.following', ['tab' => 'organizations'])); ?>" 
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors <?php echo e($currentTab === 'organizations' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                            <span>Organizations</span>
                            <?php if(isset($organizations)): ?>
                                <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs"><?php echo e($organizations->total()); ?></span>
                            <?php endif; ?>
                        </div>
                    </a>
                    <a href="<?php echo e(route('follow-management.following', ['tab' => 'groups'])); ?>" 
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors <?php echo e($currentTab === 'groups' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <span>Groups</span>
                            <?php if(isset($groups)): ?>
                                <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs"><?php echo e($groups->total()); ?></span>
                            <?php endif; ?>
                        </div>
                    </a>
                    <a href="<?php echo e(route('follow-management.following', ['tab' => 'discover'])); ?>"
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors <?php echo e($currentTab === 'discover' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?>">
                        <div class="flex items-center space-x-2">
                            <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Discover_Users','class' => 'w-5 h-5']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Discover_Users','class' => 'w-5 h-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                            <span>Discover Users</span>
                            <?php if(isset($allUsers)): ?>
                                <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs"><?php echo e($allUsers->total()); ?></span>
                            <?php endif; ?>
                        </div>
                    </a>
                </nav>
            </div>

            <!-- Search Bar -->
            <div class="p-6">
                <form method="GET" action="<?php echo e(route('follow-management.following')); ?>" class="flex items-center space-x-4">
                    <input type="hidden" name="tab" value="<?php echo e($currentTab); ?>">
                    <div class="flex-1">
                        <div class="relative">
                            <input type="text"
                                   name="search"
                                   value="<?php echo e($search); ?>"
                                   placeholder="<?php if($currentTab === 'discover'): ?>Search users by name, email, or bio...<?php else: ?> Search <?php echo e($currentTab); ?>...<?php endif; ?>"
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Search','class' => 'h-5 w-5 text-gray-400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Search','class' => 'h-5 w-5 text-gray-400']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <button type="submit" 
                            class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        Search
                    </button>
                    <?php if($search): ?>
                        <a href="<?php echo e(route('follow-management.following', ['tab' => $currentTab])); ?>" 
                           class="text-gray-500 hover:text-gray-700 px-3 py-2 transition-colors">
                            Clear
                        </a>
                    <?php endif; ?>
                </form>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="bg-white rounded-lg shadow-sm">
            <?php if($currentTab === 'users' && isset($following)): ?>
                <?php echo $__env->make('follow-management.partials.users-tab', ['items' => $following], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php elseif($currentTab === 'organizations' && isset($organizations)): ?>
                <?php echo $__env->make('follow-management.partials.organizations-tab', ['items' => $organizations], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php elseif($currentTab === 'groups' && isset($groups)): ?>
                <?php echo $__env->make('follow-management.partials.groups-tab', ['items' => $groups], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php elseif($currentTab === 'discover' && isset($allUsers)): ?>
                <?php echo $__env->make('follow-management.partials.discover-tab', ['items' => $allUsers], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $attributes = $__attributesOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__attributesOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $component = $__componentOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__componentOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/follow-management/following.blade.php ENDPATH**/ ?>