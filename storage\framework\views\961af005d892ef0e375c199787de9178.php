<?php if (isset($component)) { $__componentOriginal4969f54a92451522b65593c595a4fb0c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4969f54a92451522b65593c595a4fb0c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.unilink-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">My Organizations</h1>
                <p class="text-gray-600 mt-1">Manage your organization memberships and invitations</p>
            </div>
            <a href="<?php echo e(route('organizations.index')); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                Browse Organizations
            </a>
        </div>
    </div>

    <!-- Note: Pending invitations section removed as organization memberships are now automatically accepted -->

    <!-- My Organizations -->
    <div class="mb-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">My Organizations (<?php echo e($myOrganizations->count()); ?>)</h2>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php $__empty_1 = true; $__currentLoopData = $myOrganizations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $organization): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                <!-- Cover Image -->
                <div class="h-32 bg-gradient-to-r from-blue-500 to-purple-600 relative">
                    <?php if($organization->cover_image): ?>
                        <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($organization->cover_image)); ?>" alt="<?php echo e($organization->name); ?>" class="w-full h-full object-cover">
                    <?php endif; ?>
                    
                    <!-- Role Badge -->
                    <div class="absolute top-2 right-2">
                        <?php
                            $role = $organization->pivot->role;
                            $badgeColor = match($role) {
                                'president' => 'bg-purple-100 text-purple-800',
                                'officer' => 'bg-blue-100 text-blue-800',
                                default => 'bg-gray-100 text-gray-800'
                            };
                        ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($badgeColor); ?>">
                            <?php echo e(ucfirst($role)); ?>

                        </span>
                    </div>
                    
                    <!-- Logo -->
                    <div class="absolute -bottom-6 left-4">
                        <div class="w-12 h-12 bg-white rounded-lg shadow-md flex items-center justify-center">
                            <?php if($organization->logo): ?>
                                <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($organization->logo)); ?>" alt="<?php echo e($organization->name); ?>" class="w-10 h-10 rounded-lg object-cover">
                            <?php else: ?>
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <span class="text-blue-600 font-semibold text-sm"><?php echo e(substr($organization->name, 0, 2)); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Content -->
                <div class="p-4 pt-8">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="text-lg font-semibold text-gray-900 truncate"><?php echo e($organization->name); ?></h3>
                        <span class="text-sm text-gray-500"><?php echo e($organization->active_members_count); ?> members</span>
                    </div>
                    
                    <p class="text-gray-600 text-sm mb-4 line-clamp-2"><?php echo e(Str::limit($organization->description, 100)); ?></p>
                    
                    <!-- Membership Info -->
                    <div class="flex items-center text-xs text-gray-500 mb-4">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9a2 2 0 012-2h3z" />
                        </svg>
                        Joined <?php echo e($organization->pivot->joined_at ? $organization->pivot->joined_at->format('M Y') : 'Recently'); ?>

                    </div>

                    <!-- Actions -->
                    <div class="flex gap-2">
                        <a href="<?php echo e(route('organizations.show', $organization)); ?>" class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            View
                        </a>
                        
                        <?php if(in_array($organization->pivot->role, ['officer', 'president']) || auth()->user()->isAdmin()): ?>
                            <a href="<?php echo e(route('organizations.edit', $organization)); ?>" class="flex-1 bg-gray-600 text-white text-center py-2 px-4 rounded-lg text-sm font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500">
                                Manage
                            </a>
                        <?php endif; ?>
                        
                        <?php if($organization->pivot->role !== 'president'): ?>
                            <form action="<?php echo e(route('organizations.leave', $organization)); ?>" method="POST" class="flex-1" onsubmit="return confirm('Are you sure you want to leave this organization?')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="w-full bg-red-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                                    Leave
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-full text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No organizations yet</h3>
                <p class="mt-1 text-sm text-gray-500">Join your first organization to get started.</p>
                <div class="mt-6">
                    <a href="<?php echo e(route('organizations.index')); ?>" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Browse Organizations
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $attributes = $__attributesOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__attributesOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $component = $__componentOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__componentOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/organizations/my.blade.php ENDPATH**/ ?>