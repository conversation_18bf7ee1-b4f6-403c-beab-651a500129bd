<?php if($items->count() > 0): ?>
    <div class="divide-y divide-gray-200">
        <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="p-6 flex items-center justify-between hover:bg-gray-50 transition-colors">
                <div class="flex items-center space-x-4">
                    <img src="<?php echo e($user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&color=7BC74D&background=EEEEEE&size=128'); ?>"" 
                         alt="<?php echo e($user->name); ?>" 
                         class="w-16 h-16 rounded-full object-cover">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <a href="<?php echo e(route('profile.user', $user)); ?>"
                               class="hover:text-blue-600 transition-colors">
                                <?php echo e($user->name); ?>

                            </a>
                        </h3>
                        <?php if($user->hasCompleteProfile()): ?>
                            <p class="text-gray-600"><?php echo e($user->email); ?></p>
                            <?php if($user->bio): ?>
                                <p class="text-sm text-gray-500 mt-1"><?php echo e(Str::limit($user->bio, 100)); ?></p>
                            <?php endif; ?>
                            <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                <span><?php echo e($user->followers()->count()); ?> followers</span>
                                <span><?php echo e($user->following()->count()); ?> following</span>
                                <span><?php echo e($user->posts()->count()); ?> posts</span>
                                <span>Following since <?php echo e($user->pivot->created_at->diffForHumans()); ?></span>
                            </div>
                        <?php else: ?>
                            <p class="text-sm text-gray-500">Basic profile</p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('user-follower', ['user' => $user]);

$__html = app('livewire')->mount($__name, $__params, 'following-user-' . $user->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    <a href="<?php echo e(route('profile.user', $user)); ?>" 
                       class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                        View Profile
                    </a>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200">
        <?php echo e($items->links()); ?>

    </div>
<?php else: ?>
    <div class="p-12 text-center">
        <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">
            <?php if(request('search')): ?>
                No users found
            <?php else: ?>
                Not following anyone yet
            <?php endif; ?>
        </h3>
        <p class="text-gray-600">
            <?php if(request('search')): ?>
                Try adjusting your search terms.
            <?php else: ?>
                Start following users to see them here.
            <?php endif; ?>
        </p>
        <?php if(request('search')): ?>
            <div class="mt-4">
                <a href="<?php echo e(route('follow-management.following', ['tab' => 'users'])); ?>" 
                   class="text-blue-600 hover:text-blue-800 font-medium">
                    View all following
                </a>
            </div>
        <?php else: ?>
            <div class="mt-4">
                <a href="<?php echo e(route('dashboard')); ?>" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Discover Users
                </a>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/follow-management/partials/users-tab.blade.php ENDPATH**/ ?>