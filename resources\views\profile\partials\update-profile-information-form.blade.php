
<section x-data="{
    skillsInput: '',
    achievementsInput: '',
    skills: {{ json_encode($user->skills_interests ?? []) }},
    achievements: {{ json_encode($user->achievements ?? []) }},
    socialLinks: {{ json_encode($user->social_links ?? []) }},
    profileVisibility: {{ json_encode($user->profile_visibility ?? [
        'academic_info' => true,
        'personal_info' => false,
        'contact_info' => false,
        'skills_interests' => true,
        'social_links' => true,
        'achievements' => true
    ]) }},
    addSkill() {
        if (this.skillsInput.trim() && !this.skills.includes(this.skillsInput.trim())) {
            this.skills.push(this.skillsInput.trim());
            this.skillsInput = '';
        }
    },
    removeSkill(index) {
        this.skills.splice(index, 1);
    },
    addAchievement() {
        if (this.achievementsInput.trim() && !this.achievements.includes(this.achievementsInput.trim())) {
            this.achievements.push(this.achievementsInput.trim());
            this.achievementsInput = '';
        }
    },
    removeAchievement(index) {
        this.achievements.splice(index, 1);
    },
    addSocialLink() {
        this.socialLinks.push({ platform: '', url: '' });
    },
    removeSocialLink(index) {
        this.socialLinks.splice(index, 1);
    }
}">

    <div class="mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-2">Edit Profile</h2>
        <p class="text-sm text-gray-600">
            {{ __("Update your profile information and customize what others can see.") }}
        </p>
    </div>

    <form id="send-verification" method="post" action="{{ route('verification.send') }}">
        @csrf
    </form>

    <form method="post" action="{{ route('profile.update') }}" class="space-y-8" enctype="multipart/form-data">
        @csrf
        @method('patch')

        <!-- Hidden inputs for dynamic arrays -->
        <template x-for="(skill, index) in skills" :key="index">
            <input type="hidden" :name="'skills_interests[' + index + ']'" :value="skill">
        </template>
        <template x-for="(achievement, index) in achievements" :key="index">
            <input type="hidden" :name="'achievements[' + index + ']'" :value="achievement">
        </template>
        <template x-for="(link, index) in socialLinks" :key="index">
            <div>
                <input type="hidden" :name="'social_links[' + index + '][platform]'" :value="link.platform">
                <input type="hidden" :name="'social_links[' + index + '][url]'" :value="link.url">
            </div>
        </template>
        <template x-for="(value, key) in profileVisibility" :key="key">
            <input type="hidden" :name="'profile_visibility[' + key + ']'" :value="value ? '1' : '0'">
        </template>

        <!-- Profile Photos Section -->
        <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">Profile Photos</h3>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Avatar Upload -->
                <div>
                    <x-input-label for="avatar" :value="__('Profile Picture')" />
                    <div class="mt-2 flex items-center space-x-4">
                        <img class="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                             src="{{ $user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&color=7BC74D&background=EEEEEE&size=64' }}"
                             alt="{{ $user->name }}">
                        <input type="file" id="avatar" name="avatar" accept="image/*" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-custom-green file:text-white hover:file:bg-opacity-90">
                    </div>
                    <x-input-error class="mt-2" :messages="$errors->get('avatar')" />
                </div>

                <!-- Background Photo Upload -->
                <div>
                    <x-input-label for="background_photo" :value="__('Background Photo')" />
                    <div class="mt-2">
                        @if($user->background_photo)
                            <div class="mb-3">
                                <img class="w-full h-32 object-cover rounded-lg border border-gray-200"
                                     src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($user->background_photo) }}"
                                     alt="Current background photo">
                            </div>
                        @endif
                        <input type="file" id="background_photo" name="background_photo" accept="image/*" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-custom-green file:text-white hover:file:bg-opacity-90">
                        <p class="mt-1 text-sm text-gray-500">Upload a background photo for your profile (max 5MB)</p>
                    </div>
                    <x-input-error class="mt-2" :messages="$errors->get('background_photo')" />
                </div>
            </div>
        </div>
        <!-- Basic Information Section -->
        <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">Basic Information</h3>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <x-input-label for="name" :value="__('Full Name')" />
                    <x-text-input id="name" name="name" type="text" class="mt-1 block w-full" :value="old('name', $user->name)" required autocomplete="name" />
                    <x-input-error class="mt-2" :messages="$errors->get('name')" />
                </div>

                <div>
                    <x-input-label for="email" :value="__('Email Address')" />
                    <x-text-input id="email" name="email" type="email" class="mt-1 block w-full" :value="old('email', $user->email)" required autocomplete="username" />
                    <x-input-error class="mt-2" :messages="$errors->get('email')" />

                    @if ($user instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && ! $user->hasVerifiedEmail())
                        <div class="mt-2">
                            <p class="text-sm text-amber-600 bg-amber-50 p-2 rounded-md">
                                {{ __('Your email address is unverified.') }}
                                <button form="send-verification" class="underline text-amber-700 hover:text-amber-900 font-medium">
                                    {{ __('Click here to re-send the verification email.') }}
                                </button>
                            </p>

                            @if (session('status') === 'verification-link-sent')
                                <p class="mt-2 font-medium text-sm text-green-600 bg-green-50 p-2 rounded-md">
                                    {{ __('A new verification link has been sent to your email address.') }}
                                </p>
                            @endif
                        </div>
                    @endif
                </div>

                <div class="md:col-span-2">
                    <x-input-label for="bio" :value="__('Bio')" />
                    <textarea id="bio" name="bio" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green" placeholder="Tell us about yourself...">{{ old('bio', $user->bio) }}</textarea>
                    <x-input-error class="mt-2" :messages="$errors->get('bio')" />
                </div>
            </div>
        </div>

        <!-- 📘 Academic Information Section -->
        <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">📘 Academic Information</h3>
                </div>
                <div class="flex items-center">
                    <label class="text-sm text-gray-600 mr-2">Public</label>
                    <input type="checkbox" x-model="profileVisibility.academic_info" class="rounded border-gray-300 text-custom-green focus:ring-custom-green">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <x-input-label for="student_id" :value="__('Student ID')" />
                    <x-text-input id="student_id" name="student_id" type="text" class="mt-1 block w-full" :value="old('student_id', $user->student_id)" autocomplete="off" />
                    <x-input-error class="mt-2" :messages="$errors->get('student_id')" />
                </div>

                <div>
                    <x-input-label for="campus" :value="__('Campus')" />
                    <x-text-input id="campus" name="campus" type="text" class="mt-1 block w-full" :value="old('campus', $user->campus)" placeholder="e.g., Main Campus, North Campus" />
                    <x-input-error class="mt-2" :messages="$errors->get('campus')" />
                </div>

                <div>
                    <x-input-label for="course_program" :value="__('Course / Program')" />
                    <x-text-input id="course_program" name="course_program" type="text" class="mt-1 block w-full" :value="old('course_program', $user->course_program)" placeholder="e.g., Bachelor of Science in Computer Science" />
                    <x-input-error class="mt-2" :messages="$errors->get('course_program')" />
                </div>

                <div>
                    <x-input-label for="year_level" :value="__('Year Level')" />
                    <select id="year_level" name="year_level" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green">
                        <option value="">Select Year Level</option>
                        <option value="1st Year" {{ old('year_level', $user->year_level) === '1st Year' ? 'selected' : '' }}>1st Year</option>
                        <option value="2nd Year" {{ old('year_level', $user->year_level) === '2nd Year' ? 'selected' : '' }}>2nd Year</option>
                        <option value="3rd Year" {{ old('year_level', $user->year_level) === '3rd Year' ? 'selected' : '' }}>3rd Year</option>
                        <option value="4th Year" {{ old('year_level', $user->year_level) === '4th Year' ? 'selected' : '' }}>4th Year</option>
                        <option value="5th Year" {{ old('year_level', $user->year_level) === '5th Year' ? 'selected' : '' }}>5th Year</option>
                        <option value="Graduate" {{ old('year_level', $user->year_level) === 'Graduate' ? 'selected' : '' }}>Graduate</option>
                    </select>
                    <x-input-error class="mt-2" :messages="$errors->get('year_level')" />
                </div>

                <div>
                    <x-input-label for="enrollment_status" :value="__('Enrollment Status')" />
                    <select id="enrollment_status" name="enrollment_status" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green">
                        <option value="">Select Status</option>
                        <option value="active" {{ old('enrollment_status', $user->enrollment_status) === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="loa" {{ old('enrollment_status', $user->enrollment_status) === 'loa' ? 'selected' : '' }}>Leave of Absence (LOA)</option>
                        <option value="graduated" {{ old('enrollment_status', $user->enrollment_status) === 'graduated' ? 'selected' : '' }}>Graduated</option>
                    </select>
                    <x-input-error class="mt-2" :messages="$errors->get('enrollment_status')" />
                </div>

                <div>
                    <x-input-label for="college_department" :value="__('College / Department')" />
                    <x-text-input id="college_department" name="college_department" type="text" class="mt-1 block w-full" :value="old('college_department', $user->college_department)" placeholder="e.g., College of Computer Studies" />
                    <x-input-error class="mt-2" :messages="$errors->get('college_department')" />
                </div>
            </div>
        </div>
        <!-- 🧍‍♂️ Personal Information Section -->
        <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">🧍‍♂️ Personal Information</h3>
                </div>
                <div class="flex items-center">
                    <label class="text-sm text-gray-600 mr-2">Public</label>
                    <input type="checkbox" x-model="profileVisibility.personal_info" class="rounded border-gray-300 text-custom-green focus:ring-custom-green">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <x-input-label for="contact_number" :value="__('Contact Number')" />
                    <x-text-input id="contact_number" name="contact_number" type="tel" class="mt-1 block w-full" :value="old('contact_number', $user->contact_number)" placeholder="+63 ************" />
                    <x-input-error class="mt-2" :messages="$errors->get('contact_number')" />
                    <p class="mt-1 text-xs text-gray-500">Optional - Your contact number for networking</p>
                </div>

                <div>
                    <x-input-label for="birthdate" :value="__('Birthdate')" />
                    <x-text-input id="birthdate" name="birthdate" type="date" class="mt-1 block w-full" :value="old('birthdate', $user->birthdate ? $user->birthdate->format('Y-m-d') : '')" />
                    <x-input-error class="mt-2" :messages="$errors->get('birthdate')" />
                </div>

                <div class="md:col-span-2">
                    <x-input-label for="address" :value="__('Address')" />
                    <textarea id="address" name="address" rows="2" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green" placeholder="Your address (optional)">{{ old('address', $user->address) }}</textarea>
                    <x-input-error class="mt-2" :messages="$errors->get('address')" />
                    <p class="mt-1 text-xs text-gray-500">Optional - Your current address</p>
                </div>

                <div class="md:col-span-2">
                    <x-input-label for="phone" :value="__('Phone Number (Legacy)')" />
                    <x-text-input id="phone" name="phone" type="tel" class="mt-1 block w-full" :value="old('phone', $user->phone)" autocomplete="tel" />
                    <x-input-error class="mt-2" :messages="$errors->get('phone')" />
                    <p class="mt-1 text-xs text-gray-500">This field will be migrated to Contact Number above</p>
                </div>
            </div>
        </div>

        <!-- 🧠 Platform-Specific Info Section -->
        <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">🧠 Platform-Specific Info</h3>
                </div>
                <div class="flex items-center">
                    <label class="text-sm text-gray-600 mr-2">Public</label>
                    <input type="checkbox" x-model="profileVisibility.skills_interests" class="rounded border-gray-300 text-custom-green focus:ring-custom-green">
                </div>
            </div>

            <div>
                <x-input-label for="skills_interests" :value="__('Skills / Interests')" />
                <div class="mt-2">
                    <div class="flex flex-wrap gap-2 mb-3">
                        <template x-for="(skill, index) in skills" :key="index">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-custom-green/10 text-custom-green border border-custom-green/20">
                                <span x-text="skill"></span>
                                <button type="button" @click="removeSkill(index)" class="ml-2 text-custom-green hover:text-red-500">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </span>
                        </template>
                    </div>
                    <div class="flex gap-2">
                        <x-text-input x-model="skillsInput" @keydown.enter.prevent="addSkill()" type="text" class="flex-1" placeholder="Add a skill or interest (e.g., Programming, Photography, Music)" />
                        <button type="button" @click="addSkill()" class="px-4 py-2 bg-custom-green text-white rounded-md hover:bg-opacity-90 transition-colors">
                            Add
                        </button>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">Press Enter or click Add to include skills and interests</p>
                </div>
                <x-input-error class="mt-2" :messages="$errors->get('skills_interests')" />
            </div>
        </div>
        <!-- 🌐 Optional Additions Section -->
        <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-cyan-100 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900">🌐 Optional Additions</h3>
            </div>

            <!-- Social Links -->
            <div class="mb-6">
                <div class="flex items-center justify-between mb-3">
                    <x-input-label :value="__('Social Links')" />
                    <div class="flex items-center">
                        <label class="text-sm text-gray-600 mr-2">Public</label>
                        <input type="checkbox" x-model="profileVisibility.social_links" class="rounded border-gray-300 text-custom-green focus:ring-custom-green">
                    </div>
                </div>
                <div class="space-y-3">
                    <template x-for="(link, index) in socialLinks" :key="index">
                        <div class="flex gap-3 items-start">
                            <select x-model="link.platform" class="w-1/3 border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green">
                                <option value="">Select Platform</option>
                                <option value="LinkedIn">LinkedIn</option>
                                <option value="Facebook">Facebook</option>
                                <option value="Twitter">Twitter</option>
                                <option value="Instagram">Instagram</option>
                                <option value="GitHub">GitHub</option>
                                <option value="Portfolio">Portfolio</option>
                                <option value="Other">Other</option>
                            </select>
                            <input x-model="link.url" type="url" placeholder="https://..." class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green">
                            <button type="button" @click="removeSocialLink(index)" class="px-3 py-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                            </button>
                        </div>
                    </template>
                    <button type="button" @click="addSocialLink()" class="w-full py-2 border-2 border-dashed border-gray-300 rounded-md text-gray-600 hover:border-custom-green hover:text-custom-green transition-colors">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        Add Social Link
                    </button>
                </div>
                <x-input-error class="mt-2" :messages="$errors->get('social_links')" />
            </div>

            <!-- Achievements -->
            <div>
                <div class="flex items-center justify-between mb-3">
                    <x-input-label :value="__('Achievements')" />
                    <div class="flex items-center">
                        <label class="text-sm text-gray-600 mr-2">Public</label>
                        <input type="checkbox" x-model="profileVisibility.achievements" class="rounded border-gray-300 text-custom-green focus:ring-custom-green">
                    </div>
                </div>
                <div class="mt-2">
                    <div class="flex flex-wrap gap-2 mb-3">
                        <template x-for="(achievement, index) in achievements" :key="index">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-yellow-100 text-yellow-800 border border-yellow-200">
                                <span x-text="achievement"></span>
                                <button type="button" @click="removeAchievement(index)" class="ml-2 text-yellow-600 hover:text-red-500">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </span>
                        </template>
                    </div>
                    <div class="flex gap-2">
                        <x-text-input x-model="achievementsInput" @keydown.enter.prevent="addAchievement()" type="text" class="flex-1" placeholder="Add an achievement (e.g., Dean's List, Contest Winner, Certification)" />
                        <button type="button" @click="addAchievement()" class="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors">
                            Add
                        </button>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">Press Enter or click Add to include achievements and certifications</p>
                </div>
                <x-input-error class="mt-2" :messages="$errors->get('achievements')" />
            </div>
        </div>

        <!-- Save Button -->
        <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-lg font-medium text-gray-900">Save Changes</h4>
                    <p class="text-sm text-gray-600">Your profile information will be updated immediately.</p>
                </div>
                <div class="flex items-center gap-4">
                    @if (session('status') === 'profile-updated')
                        <p x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 3000)" class="text-sm text-green-600 bg-green-50 px-3 py-1 rounded-md">
                            {{ __('Profile updated successfully!') }}
                        </p>
                    @endif
                    <x-primary-button class="px-8 py-3">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        {{ __('Save Profile') }}
                    </x-primary-button>
                </div>
            </div>
        </div>
    </form>
</section>
